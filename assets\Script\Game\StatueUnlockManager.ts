// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import Unit from "../GameStuffAndComps/Unit";
import GameManager from "../GameManager";
import GameUtils from "./GameUtils";
import LocalUtils, { TweenObject } from "../LocalUtils";
import GameDirector from "./GameDirector";
import GameStuffManager from "./GameStuffManager";
import Unlock<PERSON>rogressUI from "./UnlockProgressUI";


export default class StatueUnlockManager {

    public static get instance(): StatueUnlockManager {
        if(!this._instance) {
            this._instance = new StatueUnlockManager();
        }
        return this._instance;
    }
    private static _instance: StatueUnlockManager = null;

    statueUnlockProgressUIList: UnlockProgressUI[] = [];

    statueUnlockNeedConfig: number[][] = [
        [10, 25, 40],
        [10, 25, 40],
    ];

    statueUnlockCurConfigIndex: number[] = [0, 0];
    statueUnlockProgressList: number[] = [0, 0];
    statueUnlockMaxProgressList: number[] = [25, 50];


    floorUnlockAreaUINodeList: cc.Node[] = [];

    isFloorUnlockAreaUnlock = new Array(15).fill(false);
    isFloorUnlockAreaUnlockCompletely = new Array(15).fill(false);
    floorUnlockAreaProgressList: number[] =    [0,  0,  0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0];
    floorUnlockAreaMaxProgressList: number[] = [10, 10, 25, 10, 10, 5, 5, 10, 10, 15,  15, 15, 15, 20, 20, 50, 50, 50, 50, 50, 60, 60, 80];

    floorUnlockCurConfigLevel: number[] = [0, 0, 0, 0];

    isAllStatuesUnlockCalled = false;
    isAllFloorUnlockAreaUnlockCalled = false;

    protected updateCallbackId = -1;

    // constructor() {
    // }

    GetUnlockListIndexFromStatueRef(statueRef: number): number {
        let index = -1;
        if(statueRef == 1) {
        } else if(statueRef == 201 || statueRef == 202) {
            index = statueRef - 201;
        }
        return index;
    }

    StatueUnlock(statueRef: number) {
        let index = this.GetUnlockListIndexFromStatueRef(statueRef);
        console.log(`#### 解锁雕像 ${statueRef} !!!`);
        if(statueRef == 1) {
            // LocalUtils.PlaySound('level_up');
            // GameUtils.rootHeroControl.SetPos(cc.v2(120, -800));
            // GameUtils.rootGameWorld.whiteLight.opacity = 180;
            // GameUtils.rootHeroControl.isCanHeroMove = false;
            // GameUtils.rootGameWorldUI.PlayTP(cc.v2(120, -800));
            // // cc.tween(new TweenObject<number>(-100, (value: number)=>{
            // //     GameUtils.mainHero.script.attribute.moveSpeedUp = value;
            // // })).delay(0.8).call(()=>{
            // //     GameUtils.rootHeroControl.isCanHeroMove = true;
            // // }).to(0.2, {value: 0}).start();
            // GameManager.instance.LateTimeCallOnce(()=>{
            //     GameUtils.rootHeroControl.isCanHeroMove = true;
            //     GameUtils.mainHero.script.attribute.AddASlowingEffect(-70, 0.2);
            // }, 0.8);
            // GameUtils.rootEnemyCreator.StartCreate();
            // GameDirector.instance.OnHeroTPIn();
            // return;

            // GameUtils.rootEnemyCreator.StartCreate();
            return;
        } else if(statueRef == 2) {
        }
        this.statueUnlockProgressList[index] = this.statueUnlockMaxProgressList[index];
        let statue = GameUtils.rootGameWorld.statues[index + 1];
        let ui = this.statueUnlockProgressUIList[index + 1];
        // statue.ReadyToDestroy();

        // if(!this._isAllowBoss) {
        //     this._isAllowBoss = true;
        //     GameUtils.rootEnemyCreator.AllowCreateBoss();
        // }
        let generatePos = statue.rootPosition.clone().add(cc.v2(0, -80));
        // if(ui) {
        //     GameManager.instance.LateTimeCallOnce(()=>{
        //         ui.node.active = false;
        //     }, 1.5);
        // }
        this.statueUnlockCurConfigIndex[index] += 1;
        let curConfigIndex = this.statueUnlockCurConfigIndex[index];
        statue.OnUnlock();
        GameManager.instance.LateFrameCall(()=>{
            GameManager.instance.LateTimeCallOnce(()=>{
                if(index == 1) {
                    if(!GameDirector.instance.isGameOver) {
                        GameUtils.OpenHeroChoosePanel(generatePos);
                    }
                } else if(index == 0) {
                    GameUtils.rootNPCCreator.CreateANPCLogger(generatePos);
                } else {
                }
                
                GameManager.instance.LateTimeCallOnce(()=>{
                    if(statueRef == 201) {
                        GameUtils.rootGuideToStatue.isStatueFirstUnlocked1 = true;
                        GameUtils.rootGuideToStatue.statue1UnlockTimes += 1;
                    } else if(statueRef == 202) {
                        GameUtils.rootGuideToStatue.isStatueFirstUnlocked2 = true;
                    }
                }, 0.2);
            }, 0.5);
            if((index == 1 && curConfigIndex <= 3) || (index == 0)) {
                GameManager.instance.LateTimeCallOnce(()=>{
                    let maxProgress = 100;
                    if(curConfigIndex > 8) {
                        maxProgress = this.statueUnlockNeedConfig[index][8] + (curConfigIndex - 8) * 5;
                    } else {
                        maxProgress = this.statueUnlockNeedConfig[index][curConfigIndex];
                    }
                    let ui = this.statueUnlockProgressUIList[index + 1];
                    if(ui) {
                        ui.SetProgressMax(maxProgress);
                    }
                    this.statueUnlockMaxProgressList[index] = maxProgress;
                    this.statueUnlockProgressList[index] = 0;
                    statue.unlockMaxProgress = maxProgress;
                    statue.unlockProgress = 0;
                    statue.isUnlockFinished = false;
                    this.RefreshStatueUnlockUI();
                }, (index == 0) ? 0.6 : 1.8);
            } else if(index < 0) {
            } else {
                // statue.isUnlockFinished = true;
                GameManager.instance.LateTimeCallOnce(()=>{
                    this.HideStatueUnlockUI(index);
                    GameUtils.rootGameStuffs.newUnlockNodes_6.find(e => e.name == 'building_board_3').active = false;
                }, 0.6);
            }
        });
        // let statues = GameUtils.rootGameWorld.statues;
        // if(!this.isAllStatuesUnlockCalled && statues[1].isUnlockFinished && statues[2].isUnlockFinished && statues[3].isUnlockFinished && statues[4].isUnlockFinished) {
        //     console.log(`#### 全部雕像解锁！`);
        //     this.isAllStatuesUnlockCalled = true;
        //     GameDirector.instance.OnAllStatuesUnlock();
        //     if(this.isAllFloorUnlockAreaUnlockCalled) {
        //         GameDirector.instance.OnAllStatuesAndFloorUnlockAreaUnlock();
        //     }
        // }
    }

    AddStatueUnlockProgress(statueRef: number, value: number) {
        let index = this.GetUnlockListIndexFromStatueRef(statueRef);
        this.statueUnlockProgressList[index] += value;
        this.RefreshStatueUnlockUI();
        if(this.statueUnlockProgressList[index] >= this.statueUnlockMaxProgressList[index]) {
            this.StatueUnlock(statueRef);
        }
    }

    AddTowerUnlockProgress(tower_id: number, value: number) {
        let index = tower_id;
        this.statueUnlockProgressList[index] += value;
        this.RefreshStatueUnlockUI();
        if(this.statueUnlockProgressList[index] >= this.statueUnlockMaxProgressList[index]) {
            this.StatueUnlock(index + 2);
        }
    }

    InitStatueUnlockUI() {
        this.statueUnlockMaxProgressList.forEach((e, index)=>{
            let script = this.statueUnlockProgressUIList[index + 1];
            if(script) {
                script.SetProgressMax(e);
            }
            let statue = GameUtils.rootGameWorld.statues[index + 1];
            if(statue) {
                statue.unlockMaxProgress = e;
            }
        });
    }

    RefreshStatueUnlockUI() {
        this.statueUnlockProgressList.forEach((e, index)=>{
            let script = this.statueUnlockProgressUIList[index + 1];
            if(script) {
                script.SetProgress(e);
            }
        });
    }

    HideStatueUnlockUI(index: number) {
        let script = this.statueUnlockProgressUIList[index + 1];
        if(script) {
            script.HideSelf();
        }
    }

    FloorUnlockAreaUnlock(floorUnlockAreaRef: number) {
        let index = floorUnlockAreaRef - 1;
        let floorUnlockAreaList = GameUtils.rootGameWorld.floorUnlockAreaList;
        
        console.log(`#### 解锁地面 UI ${floorUnlockAreaRef} !!!`);
        LocalUtils.PlaySoundDelay(0.1, 'bling');

        if(floorUnlockAreaRef == 1) { // 解锁小助理
            GameManager.instance.LateTimeCallOnce(()=>{
                GameUtils.rootNPCCreator.CreateANPCWaiter(undefined);
            }, 0.5);
        } else if(floorUnlockAreaRef == 2) { // 解锁小助理
            GameManager.instance.LateTimeCallOnce(()=>{
                GameUtils.rootNPCCreator.CreateANPCWaiter(undefined, true);
            }, 0.5);
        } else if(floorUnlockAreaRef == 3) { // 解锁扩展
            GameManager.instance.LateTimeCallOnce(()=>{
                GameDirector.instance.TrySetGameStateTrue('unlock_expand_1');
            }, 0.5);
        } else if(floorUnlockAreaRef >= 4 && floorUnlockAreaRef <= 5) { // 解锁收银员
            if(floorUnlockAreaRef == 4) {
                GameManager.instance.LateTimeCallOnce(()=>{
                    GameDirector.instance.TrySetGameStateTrue('unlock_npc_cashier_1');
                }, 0.5);
            } else if(floorUnlockAreaRef == 5) {
                GameManager.instance.LateTimeCallOnce(()=>{
                    GameDirector.instance.TrySetGameStateTrue('unlock_npc_cashier_2');
                }, 0.5);
            }
        } else if(floorUnlockAreaRef >= 6 && floorUnlockAreaRef <= 9) { // 解锁建筑
            let ref = floorUnlockAreaRef - 5;
            GameManager.instance.LateTimeCallOnce(()=>{
                GameDirector.instance.TrySetGameStateTrue('unlock_building_' + ref);
            }, 0.5);
        } else if(floorUnlockAreaRef >= 10 && floorUnlockAreaRef <= 13) { // 解锁塔
            let towerIndex = floorUnlockAreaRef - 10;
            GameManager.instance.LateTimeCallOnce(()=>{
                GameUtils.rootGameWorld.UnlockTower(towerIndex);
            }, 0.5);
        } else if(floorUnlockAreaRef >= 14 && floorUnlockAreaRef <= 15) { // 解锁搬运工
            let isGotoArea2 = false;
            if(floorUnlockAreaRef == 15) {
                isGotoArea2 = true;
            }
            GameManager.instance.LateTimeCallOnce(()=>{
                GameUtils.rootNPCCreator.CreateANPCPorter(undefined, isGotoArea2);
            }, 0.5);
        }

        // 可重置地面 ui
        floorUnlockAreaList[index].OnUnlock();
        let isReloadProgress = false;
        let reloadProgress = 50;
        let reloadProgressDelay = 0.2;

        if(floorUnlockAreaRef == 1 || floorUnlockAreaRef == 2) {
            let levelIndex = 0;
            if(floorUnlockAreaRef == 1) {
                levelIndex = 0;
            } else if(floorUnlockAreaRef == 2) {
                levelIndex = 1;
            }
            let level = this.floorUnlockCurConfigLevel[levelIndex];
            if(level < this.statueUnlockNeedConfig[levelIndex].length - 1) {
                this.floorUnlockCurConfigLevel[levelIndex] += 1;
                isReloadProgress = true;
                reloadProgress = this.statueUnlockNeedConfig[levelIndex][level + 1];
            } else {
                if(floorUnlockAreaRef == 1) {
                    // GameDirector.instance.TrySetGameStateTrue('first_3_digger_create');
                }
                this.HideFloorUnlockAreaUI(index);
            }
        } else {
            this.HideFloorUnlockAreaUI(index);
        }

        if(isReloadProgress) { // 可重置的地面 ui
            GameManager.instance.LateTimeCallOnce(()=>{
                let maxProgress = reloadProgress;
                floorUnlockAreaList[index].unlockProgress = 0;
                floorUnlockAreaList[index].unlockMaxProgress = maxProgress;
                this.floorUnlockAreaProgressList[index] = 0;
                this.floorUnlockAreaMaxProgressList[index] = maxProgress;
                this.RefreshFloorUnlockAreaUI(index);
            }, reloadProgressDelay);
            GameManager.instance.LateTimeCallOnce(()=>{
                floorUnlockAreaList[index].isUnlockFinished = false;
            }, reloadProgressDelay + 0.6);
        } else {
            this.isFloorUnlockAreaUnlockCompletely[index] = true;
            console.log(`### floorUnlockArea ${floorUnlockAreaRef} 解锁完全！！`);

        }

        this.isFloorUnlockAreaUnlock[index] = true;

        let isAllUnlockCompletely = true;
        for(let i = 0; i <= 14; i++) {
            if(!this.isFloorUnlockAreaUnlockCompletely[i]) {
                isAllUnlockCompletely = false;
                break;
            }
        }
        if(isAllUnlockCompletely) {
            console.log(`### 全部 floorUI 解锁完全！！`);
            GameDirector.instance.TrySetGameStateTrue('all_floorUI_unlocked');
        }
        // let isAllTableUnlock = true;
        // let isArea1UnlockFinish = true;
        // let isAllBedsUnlock = true;
        // for(let i = 2; i <= 9; i++) {
        //     if(!this.isFloorUnlockAreaUnlock[i - 1]) {
        //         isAllTableUnlock = false;
        //         break;
        //     }
        // }
        // for(let i = 2; i <= 3; i++) {
        //     if(!this.isFloorUnlockAreaUnlock[i - 1]) {
        //         isArea1UnlockFinish = false;
        //         break;
        //     }
        // }
        // for(let i = 10; i <= 17; i++) {
        //     if(!this.isFloorUnlockAreaUnlock[i - 1]) {
        //         isAllBedsUnlock = false;
        //         break;
        //     }
        // }
        // if(isAllTableUnlock) {
        //     GameDirector.instance.TrySetGameStateTrue('finish_unlocked_area_seats');
        // }
        // if(isArea1UnlockFinish) {
        //     GameDirector.instance.TrySetGameStateTrue('finish_unlocked_area_1');
        // }
        // if(isAllBedsUnlock) {
        //     GameDirector.instance.TrySetGameStateTrue('finish_unlocked_area_beds');
        // }

        // let towerUnlockNum = 0;
        // for(let i = 1; i <= 4; i++) {
        //     if(this.isFloorUnlockAreaUnlock[i]) {
        //         towerUnlockNum += 1;
        //     }
        // }
        // if(towerUnlockNum >= 2) {
        //     GameDirector.instance.TrySetGameStateTrue('unlocked_2_towers');
        // }
    }

    AddFloorUnlockAreaProgress(floorUnlockAreaRef: number, value: number) {
        let index = floorUnlockAreaRef - 1;
        this.floorUnlockAreaProgressList[index] += value;
        this.RefreshFloorUnlockAreaUI(index);
        if(this.floorUnlockAreaProgressList[index] >= this.floorUnlockAreaMaxProgressList[index]) {
            this.FloorUnlockAreaUnlock(floorUnlockAreaRef);
        }
    }

    InitFloorUnlockAreaUI() {
        this.floorUnlockAreaMaxProgressList.forEach((e, index)=>{
            let ui = this.floorUnlockAreaUINodeList[index];
            let floorUnlockArea = GameUtils.rootGameWorld.floorUnlockAreaList[index];
            if(floorUnlockArea) {
                floorUnlockArea.unlockMaxProgress = e;
                floorUnlockArea.InitUI();
            }
        });
    }

    RefreshFloorUnlockAreaUI(index: number) {
        let maxProgress = this.floorUnlockAreaMaxProgressList[index];
        // this.floorUnlockAreaMaxProgressList.forEach((e, index)=>{
        let progress = this.floorUnlockAreaProgressList[index];
        let floorUnlockArea = GameUtils.rootGameWorld.floorUnlockAreaList[index];
        if(floorUnlockArea) {
            floorUnlockArea.RefreshUI(progress, maxProgress);
        }
    }

    HideFloorUnlockAreaUI(index: number) {
        let floorUnlockArea = GameUtils.rootGameWorld.floorUnlockAreaList[index];
        if(floorUnlockArea) {
            floorUnlockArea.HideUI();
        }
    }

    // OnAbsorb(progressAdd = 1) {
    //     this.absorbProgress += progressAdd;
    //     if(this.absorbProgress >= this.absorbMaxProgress) {
    //         this.absorbProgress = this.absorbMaxProgress;
    //         this.isAbsorbProgressFull = true;
    //         if(!this.isAbsorbProgressFullCalled) {
    //             this.isAbsorbProgressFullCalled = true;
    //             GameDirector.instance.OnAbsorbProgressFull();
    //         }
    //     }
    // }

    // onDestroy() {
    //     if(this.updateCallbackId < 0) {
    //         // console.error('this.updateCallbackId < 0 !!!!');
    //     } else {
    //         GameManager.instance.RemoveUpdateCallbackByID(this.updateCallbackId);
    //     }
    // }
}
