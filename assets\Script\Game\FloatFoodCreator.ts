// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import GameManager from "../GameManager";
import GameUtils, { ObjectPool, UnitInfo } from "./GameUtils";
import LocalUtils from "../LocalUtils";
import GameWorld from "./GameWorld";
import NPC from "../GameStuffAndComps/NPC";
import GameStuffManager from "./GameStuffManager";
import FloatFood from "./Stuffs/FloatFood";
import GameDirector from "./GameDirector";

const {ccclass, property} = cc._decorator;

@ccclass
export default class FloatFoodCreator extends cc.Component {

    @property(GameWorld)
    targetGameWorld: GameWorld = null;

    @property(cc.Prefab)
    floatFoodPrefab: cc.Prefab = null;


    get nowNums(): number[] {
        return GameStuffManager.instance.floatFoodLists.map((e)=>{ return e.length; });
    };
    numLimit = 10;

    createCDTime = 0.4;
    createWaveTime = 0.1;

    // createFoodTypeRefLeftTimes = [0, 1, -1, -1];
    createFoodTypeRefLeftTimes = [0, 1, 0, 1];
    createAllTimes = 3;

    // NPCPools: ObjectPool<UnitInfo>[] = [];
    // NPCNodePools: cc.NodePool[] = [];

    isAllowCostInputResrouces: boolean[] = [false, false, false, false];

    scheduleInputResourceNum: number[] = [0, 0, 0, 0];
    inputResourceProgress: number[] = [100, 0, 60, 60];
    inputResourceMaxProgress: number[] = [100, 100, 100, 100];

    isAllowCreateFood: boolean[] = [false, false, false, false];

    private _isAllowCreate = false;
    private _createTime = 0;
    private _creatingTime = 0;
    private _isCreating = false;
    private _isCreated = false;

    // LIFE-CYCLE CALLBACKS:
    onLoad () {
        GameUtils.rootFloatFoodCreator = this;
        // this.InitNPCPools();
    }
    start () {
        GameManager.instance.AddGameUpdate('FloatFoodCreator', (dt: number)=>{
            this.gameUpdate(dt);
        });
        this.StartCreate();
    }

    StartCreate() {
        this._creatingTime = this.createWaveTime;
        this._isAllowCreate = true;
    }

    StartCreateFoodArea2() {
        this.createFoodTypeRefLeftTimes[2] = 0;
        this.createFoodTypeRefLeftTimes[3] = 3;
    }

    gameUpdate(dt: number) {
        if(this._isAllowCreate) {
            this.TryCreate(dt);
        }
    }

    CheckCanInputResource(centerBuildingRef: number) {
        let nowResourceProgress = this.inputResourceProgress[centerBuildingRef - 1];
        let singleProgress = this.GetProgressAddFromSingleResource(centerBuildingRef);
        let scheduleInputResourceProgress = this.scheduleInputResourceNum[centerBuildingRef - 1] * singleProgress;
        return nowResourceProgress + scheduleInputResourceProgress <= 99;
    }

    GetProgressAddFromSingleResource(centerBuildingRef: number) {
        let progressAdd = 20;
        if(centerBuildingRef == 1) {
            progressAdd = 20;
        } else if(centerBuildingRef == 2) {
            progressAdd = 100;
        } else if(centerBuildingRef == 3) {
            progressAdd = 20;
        } else if(centerBuildingRef == 4) {
            progressAdd = 100;
        }
        return progressAdd;
    }

    ScheduleInputResource(centerBuildingRef: number, num: number) {
        this.scheduleInputResourceNum[centerBuildingRef - 1] += num;
    }

    ScheduleFinishInputResource(centerBuildingRef: number, num: number) {
        this.scheduleInputResourceNum[centerBuildingRef - 1] -= num;

        let singleProgress = this.GetProgressAddFromSingleResource(centerBuildingRef);
        let maxProgress = this.inputResourceMaxProgress[centerBuildingRef - 1];
        this.inputResourceProgress[centerBuildingRef - 1] += singleProgress * num;
        if(this.inputResourceProgress[centerBuildingRef - 1] > maxProgress) {
            this.inputResourceProgress[centerBuildingRef - 1] = maxProgress;
        }
    }

    TryCreate(dt: number) {
        this._createTime += dt;
        if(!this._isCreated && !this._isCreating && this._createTime > this.createCDTime) {
            if(this.CheckCanCreateWave()) {
                this._createTime = 0;
                this._isCreating = true;
            }
        }
        if(this._isCreating) {
            this._creatingTime += dt;
            if(this._creatingTime > this.createWaveTime) {
                this.CreateWave();
                this._creatingTime = this._creatingTime - this.createWaveTime;
                this._isCreating = false;
                this._isCreated = false;
            }
        } else {
            this._creatingTime = 0;
        }
    }

    CheckCanCreateWave() {
        return true;
        // return this.nowNum < this.numLimit;
    }

    CreateWave() {
        for(let i = 0; i < 4; i++) {
            let nowNum = this.nowNums[i];
            if(nowNum < this.numLimit) {
                let foodLeftTime = this.createFoodTypeRefLeftTimes[i];
                if(foodLeftTime == 0) {
                    this.TryCreateAFloatFood(this.GetCreateFloatFoodPos(i + 1), i + 1);
                    this.createFoodTypeRefLeftTimes[i] = this.createAllTimes;
                } else if(foodLeftTime > 0) {
                    this.createFoodTypeRefLeftTimes[i] -= 1;
                }
            }
        }
    }

    // CreateFirstWave() {
    //     let npcs: NPC[] = [];
    //     for(let i = 0; i < 5; i++) {
    //         let npcInfo = this.CreateANPCBuyer();
    //         if(npcInfo) {
    //             npcs.push(npcInfo.script as NPC);
    //         }
    //     }
    //     npcs.forEach((e, index)=>{
    //         e.isFirstWaveBuyer = true;
    //         let trackNodes = GameUtils.rootPathPoints.GetTrackPathPoints(1);
    //         let pos1 = cc.v2(trackNodes[1].position);
    //         let pos2 = cc.v2(trackNodes[2].position);
    //         e.firstWaveBuyerTPPosition = pos2.lerp(pos1, index / 5);
    //     });
    // }

    TryCreateAFloatFood(pos?: cc.Vec2, foodTypeRef: number = 1): FloatFood {
        let progressCost = 5;
        // if(!GameDirector.instance.GetGameState('unlock_building_' + foodTypeRef)) {
        //     return null;
        // }
        if(!this.isAllowCreateFood[foodTypeRef - 1]) {
            return null;
        }
        if(this.inputResourceProgress[foodTypeRef - 1] >= progressCost) {
            this.inputResourceProgress[foodTypeRef - 1] -= progressCost;
            return this.CreateAFloatFood(pos, foodTypeRef);
        }
        return null;
    }

    CreateAFloatFood(pos?: cc.Vec2, foodTypeRef: number = 1): FloatFood {
        if(!pos) {
            // pos = cc.v2(200, -500);
            pos = cc.v2(GameUtils.rootPathPoints.floatFoodPathPoints[0].position);
        }
        let prefab = this.floatFoodPrefab;
        if(!prefab) { return null; }
        let floatFoodScript = this.GenerateAFloatFood(pos, prefab, (foodTypeRef == 2 || foodTypeRef == 4) ? 0.8 : 0);
        floatFoodScript.ChangeFoodType(foodTypeRef);
        if(foodTypeRef == 2) {
            let building = GameUtils.rootGameWorld.centerBuildings[foodTypeRef - 1];
            building.PlayMakeFood();
        } else  if(foodTypeRef == 4) {
            let building = GameUtils.rootGameWorld.centerBuildings[foodTypeRef - 1 + 1];
            building.PlayMakeFood();
        }
        // console.log(`生成食物！`);
        let listRef = foodTypeRef;
        GameStuffManager.instance.PushFloatFood(floatFoodScript, listRef);
        return floatFoodScript;
    }

    GenerateAFloatFood(pos: cc.Vec2, prefab: cc.Prefab, delayTime = 0): FloatFood {
        let floatFoodScript: FloatFood = null;
        let node = LocalUtils.GenerateNode(this.targetGameWorld.MiddleNode, prefab, cc.v3());
        floatFoodScript = node.getComponent(FloatFood);
        floatFoodScript.Init();
        floatFoodScript.OnBorn();
        GameManager.instance.LateFrameCall(()=>{
            floatFoodScript.rootPosition = pos;
            if(delayTime <= 0) {
                floatFoodScript.Appear();
            } else {
                GameManager.instance.LateTimeCallOnce(()=>{
                    node.opacity = 255;
                    floatFoodScript.Appear();
                }, delayTime);
            }
        });
        return floatFoodScript;
    }

    GetCreateFloatFoodPos(foodTypeRef: number) {
        let floatFoodPathPointRef = 1;
        if(foodTypeRef == 1) {
            floatFoodPathPointRef = 1;
        } else if(foodTypeRef == 2) {
            floatFoodPathPointRef = 2
        } else if(foodTypeRef == 3) {
            floatFoodPathPointRef = 17;
        } else if(foodTypeRef == 4) {
            floatFoodPathPointRef = 18;
        }
        return cc.v2(GameUtils.rootPathPoints.floatFoodPathPoints[floatFoodPathPointRef - 1].position);
    }

    // TryGenerateFromNodePool(enemyPrefabIndex: number): UnitInfo {
    //     let enemyInfo: UnitInfo = null;
    //     if (this.NPCPools[enemyPrefabIndex].Size() > 0) {
    //         enemyInfo = this.NPCPools[enemyPrefabIndex].Take();
    //         // console.log(`成功从对象池中取出对象！ id: ${enemyInfo.script.unit_id}`);
    //         return enemyInfo;
    //     } else {
    //         // console.log('对象池没有对象！');
    //         return null;
    //     }
    // }

    // TryRecoveryNPC(info: UnitInfo) {
    //     let index = (info.script as NPC).NPCRef - 1;
    //     if(index < 3) {
    //         this.NPCPools[index].Put(info);
    //         // console.log(`成功向对象池中加入对象！ id: ${info.script.unit_id}`);
    //         return true;
    //     }
    //     return false;
    // }

    PlayAnim() {
                    // if(!e.isBuyerUIShowed) {
                    //     // console.log('动画 show！');
                    //     e.isBuyerUIShowed = true;
                    //     barNode.scale = 0;
                    //     cc.tween(barNode).to(0.3, {scale: 1}).start();
                    // }
                    // if(!e.isBuyerBuyUITweenOver) {
                    //     // console.log('动画 play!');
                    //     e.isBuyerBuyUITweenOver = true;
                    //     if(e.buyerBuyOverProgress != 0) {
                    //         LocalUtils.PlaySound('give');
                    //     }
                    //     let progressNode = barNode.getChildByName('progress');
                    //     let mask = progressNode.getChildByName('mask');
                    //     let icon = mask.getChildByName('icon');
                    //     let bar = progressNode.getChildByName('bar');
                    //     let img_bar = bar.getComponent(cc.Sprite);
                    //     cc.tween(icon).to(0.12, {scale: 1.3}).to(0.08, {scale: 1}).start();
                    //     if(img_bar) {
                    //         cc.tween(new TweenObject(img_bar.fillRange, (value: number)=>{
                    //             img_bar.fillRange = value;
                    //         })).to(0.2, {value: e.buyerBuyOverProgress / e.buyerBuyMaxProgress}).start();
                    //     }
                    // }
                    
    }
}
