{"collision-matrix": [[false], [false, false], [false, false, false, null, false, null, null, null, null, false], [false, false, false, false, true, true, null, null, null, false], [false, false, false, true, false, true, null, null, null, false], [false, false, false, true, true, false], [false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false, false, true, true], [false, false, false, false, false, false, false, false, false, true, false]], "excluded-modules": ["Dynamic Atlas", "DragonBones", "Label Effect", "MotionStreak", "Native Socket", "<PERSON><PERSON><PERSON><PERSON>", "PageViewIndicator", "ProgressBar", "Slide<PERSON>", "StudioComponent", "Toggle", "TiledMap", "VideoPlayer", "WebView", "3D Primitive", "WechatSubContext", "SwanSubContext", "TypeScript Polyfill", "3D Physics/cannon.js", "3D Physics/Builtin", "3D Particle"], "group-list": ["default", "UI", "GameWorld", "GameWorldStuff", "GameWorldUnit", "GameWorldBullet", "UI3D1", "UI3D2", "UIFloat", "GameWorldDrifting", "GameWorldDriftingWall"], "start-scene": "2d2f792f-a40c-49bb-a189-ed176a246e49", "design-resolution-width": 960, "design-resolution-height": 640, "fit-width": false, "fit-height": true, "use-project-simulator-setting": false, "simulator-orientation": false, "use-customize-simulator": true, "simulator-resolution": {"height": 640, "width": 960}, "last-module-event-record-time": 1760439329045, "assets-sort-type": "name", "facebook": {"appID": "", "audience": {"enable": false}, "enable": false, "live": {"enable": false}}, "migrate-history": []}