// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import LocalUtils from "./LocalUtils";

const {ccclass, property} = cc._decorator;


export enum CpSDKDownloadMark {
    //下载接口中的，remark按照next、again、download、automatic_jump上报即可。
    next,
    again,
    download,
    automatic_jump,
    other,
}

@ccclass
export default class DownloadButton extends cc.Component {

    @property({type: cc.Enum(CpSDKDownloadMark)})
    cpSDKDownloadMark: CpSDKDownloadMark = CpSDKDownloadMark.download;

    @property({visible() {
        return this.cpSDKDownloadMark == CpSDKDownloadMark.other;
    }})
    mark: string = '';

    @property({tooltip: '是否在谷歌版本中隐藏'})
    hideInGoogleBranch = false;

    // button: cc.Button = null;

    // LIFE-CYCLE CALLBACKS:

    onLoad () {
        // 谷歌版本中隐藏
        if(this.hideInGoogleBranch && LocalUtils.isGoogleBranch) {
            this.node.active = false;
            return;
        }
        // this.button = this.getComponent(cc.Button);
        this.node.on('click', this.OnTouchPlayNow, this);
        this.node.on(cc.Node.EventType.TOUCH_START, this.OnTouchPlayNow, this);
    }

    OnTouchPlayNow() {
        // CpSDK.ClickDownloadBar(1);
        let mark = (this.cpSDKDownloadMark == CpSDKDownloadMark.next && 'next')
            || (this.cpSDKDownloadMark == CpSDKDownloadMark.again && 'again')
            || (this.cpSDKDownloadMark == CpSDKDownloadMark.download && 'download')
            || (this.cpSDKDownloadMark == CpSDKDownloadMark.automatic_jump && 'automatic_jump')
            || (this.cpSDKDownloadMark == CpSDKDownloadMark.other && this.mark)
            || 'download';
        console.log(`click download! mark = ${mark}`);
        LocalUtils.CpSDKDownload(mark);
    }

    start () {

    }

    // update (dt) {}
}
