// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import GameUtils from "./Game/GameUtils";
import LocalUtils from "./LocalUtils";

const {ccclass, property} = cc._decorator;

@ccclass
export default class JoystickArea extends cc.Component {

    @property(cc.Label)
    lab_string1: cc.Label = null;
    @property(cc.Label)
    lab_string2: cc.Label = null;
    @property(cc.Sprite)
    img_progress: cc.Sprite = null;

    @property(cc.Node)
    joystickNode: cc.Node = null;

    @property(cc.Node)
    rod: cc.Node = null;

    @property(cc.Node)
    guideFinger: cc.Node = null;

    @property(cc.Float)
    deadAreaRadius = 50;

    @property(cc.Float)
    maxRadius: number = 150;


    get rodDirection(): cc.Vec2 {
        return this._rodDirection;
    }

    get touchDirAngle(): number {
        return this.GetRodDirectionAngle();
    }

    get touchRadius(): number {
        return this._touchRadius;
    }

    get touchValue(): number {
        // return (this._touchRadius - this.deadAreaRadius) / (this.maxRadius - this.deadAreaRadius);
        return this.isInDeadArea ? 0 : 1;
    }

    get isInDeadArea(): boolean {
        return this._touchRadius < this.deadAreaRadius;
    }

    isAllowAutoGuide = false;

    private _onTouchStartCallback: Function = ()=>{};
    private _onTouchCallback: Function = ()=>{};
    private _onTouchEndCallback: Function = ()=>{};


    private _rodDirection = cc.v2(0, 1);
    private _touchRadius = 0;

    private _isTouching = false;
    private _isRealTouching = false;
    private _touchID = 0;
    private _touchPosOffset = cc.v2(0, 0);

    private _noTouchingTime = 0;

    private _isFirstClicked = false;

    private _isGuide = true;
    private _isGuiding = false;
    private _isGuided = false;


    // LIFE-CYCLE CALLBACKS:

    onLoad () {
        GameUtils.rootJoystickArea = this;
        this._touchRadius = this.deadAreaRadius;

        // 谷歌版本中关闭手指引导
        // this.guideFinger.active = !LocalUtils.isGoogleBranch;
        this.guideFinger.getComponent(cc.Sprite).enabled = !LocalUtils.isGoogleBranch;
        
        this.node.on(cc.Node.EventType.TOUCH_START, this.OnTouchStart, this);
        this.node.on(cc.Node.EventType.TOUCH_MOVE, this.OnTouchMove, this);
        this.node.on(cc.Node.EventType.TOUCH_END, this.OnTouchEnd, this);
        this.node.on(cc.Node.EventType.TOUCH_CANCEL, this.OnTouchEnd, this);

        this._noTouchingTime = 0;
    }

    start () {
        this.scheduleOnce(()=>{
            if(this._isGuide) {
                this.ShowGuideFinger();
            }
        }, 0.05);
    }

    update (dt: number) {
        let str1 = `dir: ${Math.floor(this.GetRodDirectionAngle())}`;
        this.lab_string1.string = str1;
        let str2 = `touchRadius: ${Math.floor(this._touchRadius)}`;
        this.lab_string2.string = str2;
        let value = this.touchValue;
        this.img_progress.fillRange = value;
        
        if(this.isAllowAutoGuide) {
            if(this._isTouching) {
                this._noTouchingTime = 0;
            } else {
                this._noTouchingTime += dt;
            }
            if(this._noTouchingTime >= 3 && !this._isGuiding) {
                this.ShowGuideFinger();
            }
        }
        if(this._isGuiding) {
            this.RefreshJoystickPosition();
        }
    }

    
    OnTouchStart(e: cc.Event.EventTouch) {
        if(!this._isFirstClicked) {
            this._isFirstClicked = true;
            LocalUtils.CpSDKEnterNext();
            LocalUtils.CpSDKClick();
            this.isAllowAutoGuide = true;
        }
        LocalUtils.RGTouch(e, 'start');

        if(!this._isTouching) {
            this._isTouching = true;
            this._touchID = e.getID();

            let canTouchTopY = this.node.height/2;
            let canTouchBottomY = -this.node.height/2;
            let viewTouchRatio = 0.32;
            let touchPos = this.joystickNode.parent.convertToNodeSpaceAR(e.getLocation());
            // if((touchPos.y - canTouchBottomY) / (canTouchTopY - canTouchBottomY) < 0.38) {
                this._isRealTouching = true;

                if(this._isGuide) {
                    this._isGuide = false;
                }
                if(this._isGuiding) {
                    this.HideGuideFinger();
                }

                let viewTouchPos = touchPos.clone();
                if((touchPos.y - canTouchBottomY) / (canTouchTopY - canTouchBottomY) > viewTouchRatio) {
                    viewTouchPos.y = canTouchBottomY + (canTouchTopY - canTouchBottomY) * viewTouchRatio;
                    this._touchPosOffset = viewTouchPos.sub(touchPos);
                } else {
                    this._touchPosOffset = cc.v2(0, 0);
                }

                this.joystickNode.active = true;
                this.joystickNode.opacity = 255;
                this.joystickNode.setPosition(viewTouchPos);


                this._touchRadius = 0;
                // this.Refresh();

                try { this._onTouchStartCallback && this._onTouchStartCallback(); } catch (error) { console.error(error); }
                try { this._onTouchCallback && this._onTouchCallback(); } catch (error) { console.error(error); }
            // }

        }
    }

    OnTouchMove(e: cc.Event.EventTouch) {
        if(e.getID() == this._touchID) {
            let touchPos = this.joystickNode.convertToNodeSpaceAR(e.getLocation());
            let viewTouchPos = touchPos.add(this._touchPosOffset);

            if(this._isRealTouching) {
                let dir = viewTouchPos.normalize();
                this._rodDirection = (dir == cc.v2()) ? this._rodDirection : dir;
                
                let srcTouchRadius = viewTouchPos.mag();
                this._touchRadius = srcTouchRadius < this.maxRadius ? srcTouchRadius : this.maxRadius;


                this.rod.setPosition(viewTouchPos.normalize().mul(this._touchRadius));
                // this.Refresh();
                try { this._onTouchCallback && this._onTouchCallback(); } catch (error) { console.error(error); }
            }
        }
    }

    OnTouchEnd(e: cc.Event.EventTouch) {
        LocalUtils.RGTouch(e, 'end');
        if(e.getID() == this._touchID) {
            if(this._isRealTouching) {
                this.rod.setPosition(cc.Vec3.ZERO);
                this.joystickNode.opacity = 0;

                try { this._onTouchCallback && this._onTouchCallback(); } catch (error) { console.error(error); }
                try { this._onTouchEndCallback && this._onTouchEndCallback(); } catch (error) { console.error(error); }
            }

            this._touchRadius = 0;
            this._isTouching = false;
            this._isRealTouching = false;
        }
    }

    ShowGuideFinger() {
        // console.log(`摇杆引导！`);
        this._isGuiding = true;
        let rodCenterPos = cc.v2();
        this.guideFinger.active = true;
        this.guideFinger.opacity = 0;
        this.guideFinger.scale = 1.15;
        this.guideFinger.position = cc.v3();
        this.joystickNode.active = true;
        this.joystickNode.opacity = 255;
        this.RefreshJoystickPosition();
        let animSpeed = 1.4;
        let animTimeScale = 1 / animSpeed;
        cc.Tween.stopAllByTarget(this.guideFinger);
        cc.tween(this.guideFinger).to(0.3 * animTimeScale, {scale: 1, opacity: 255}).then(
            cc.tween<cc.Node>().call(()=>{
                    cc.tween(this.rod).to(0.4 * animTimeScale, {position: cc.v3(rodCenterPos.add(cc.v2(70, 49)))}
                        ).bezierTo(0.3 * animTimeScale, rodCenterPos.add(cc.v2(70*1.4, 49*1.4)), rodCenterPos.add(cc.v2(85.5*1.4, 0)), rodCenterPos.add(cc.v2(85.5, 0))
                        ).to(0.6 * animTimeScale, {position: cc.v3(rodCenterPos.add(cc.v2(-85.5, 0)))}
                        ).bezierTo(0.3 * animTimeScale, rodCenterPos.add(cc.v2(-85.5*1.4, 0)), rodCenterPos.add(cc.v2(-70*1.4, -49*1.4)), rodCenterPos.add(cc.v2(-70, -49))
                        ).to(0.3 * animTimeScale, {position: cc.v3(rodCenterPos)}
                    ).start();
                }).delay(1.9 * animTimeScale
            ).union().repeat(2).delay(0.1).to(0.3 * animTimeScale, {scale: 1.15, opacity: 0}).delay(0.5)
        ).union().repeatForever().start();
    }

    HideGuideFinger() {
        this._isGuiding = false;
        this._isGuided = true;
        this.guideFinger.active = false;
        cc.Tween.stopAllByTarget(this.guideFinger);
        cc.Tween.stopAllByTarget(this.rod);
    }

    RefreshJoystickPosition() {
        let nodeWidth = this.node.width;
        let nodeHeight = this.node.height;
        let srcPos = cc.v2();
        if(nodeWidth / nodeHeight > 1) {
            srcPos.x = nodeWidth/2 - 260;
            srcPos.y = -nodeHeight/2 + 220;
        } else {
            srcPos.x = 0;
            // srcPos.y = -220;
            srcPos.y = -nodeHeight/2 + 290;
        }
        this.joystickNode.position = cc.v3(srcPos);
    }

    Refresh() {

    }

    SetOnTouchCallback(callback: Function, ref = 2) {
        if(ref == 1) {
            this._onTouchStartCallback = callback;
        } else if(ref == 3) {
            this._onTouchEndCallback = callback;
        } else {
            this._onTouchCallback = callback;
        }
    }

    // GetDir(): cc.Vec3 {
    //     let pos = this.rod.position.mul(1 / this.maxRadius)

    //     return cc.v3(pos.x, pos.y)
    // }

    GetRodDirectionAngle(): number {
        return LocalUtils.Vec2ToAngle(this._rodDirection, cc.v2(0, 1));
    }
}
