import { StringDictionary } from "../LocalUtils";

const { ccclass, property, menu, executeInEditMode } = cc._decorator;

/**
 * 自定义脚本
 * 此脚本用添加到节点组件的方式实现一些简单的功能
 * 
 */

export enum CommandsGroupType {
    none,
    example,
    spriteSync, // 替换精灵纹理（用于换皮）
    renameNodes, // 为子节点重命名
    searchUsingSelf,
}

@ccclass
@executeInEditMode
@menu('自定义命令脚本')
export default class Commands extends cc.Component {

    @property({type: cc.Enum(CommandsGroupType), override: true, displayName: '命令组选择', tooltip:
        '选择一组命令\n' +
        '⚠️ 有些命令可能是不可逆的，在执行前先设置好选项\n'
    })
    commandsGroup: CommandsGroupType = CommandsGroupType.none;

    /** 这是一个简单的命令属性，在属性检查器中表现为复选框，点击复选框执行 set 函数 */
    @property({displayName: '> 执行示例命令', tooltip: '执行示例命令', visible: function() {return this.IsVisible(0)}})
    get commandExample(): boolean {
        return false;
    }
    set commandExample(value: boolean) {
        this.CallExampleCommand();
    }


    @property({displayName: '> 替换精灵纹理', tooltip: '替换场景中使用相同纹理的精灵纹理', visible: function() {return this.IsVisible(1)}})
    get replaceAllSceneTexture(): boolean {
        return false;
    }
    set replaceAllSceneTexture(value: boolean) {
        this.ReplaceAllSceneTexture();
    }
    @property({displayName: '> 读取节点纹理', tooltip: '如果本节点是精灵节点，读取本节点的精灵纹理为源纹理', visible: function() {return this.IsVisible(1)}})
    get loadNodeSrcTexture(): boolean {
        return false;
    }
    set loadNodeSrcTexture(value: boolean) {
        this.LoadNodeSrcTexture();
    }
    @property({type: cc.Texture2D, displayName: '源纹理', tooltip: '使用本纹理的节点将会被替换纹理', visible: function() {return this.IsVisible(1)}})
    sourceTexture: cc.Texture2D = null;
    @property({type: cc.SpriteFrame, displayName: '目标精灵帧', tooltip: '替换的目标精灵帧', visible: function() {return this.IsVisible(1)}})
    targetSpriteFrame: cc.SpriteFrame = null;
    @property({displayName: '是否仅限子节点', tooltip: '替换范围仅限本节点以及所有子节点，如果关闭则为全场景', visible: function() {return this.IsVisible(1)}})
    isOnlyThisNodeChildren: boolean = true;
    @property({displayName: '是否仅限显示节点', tooltip: '替换范围仅限 active 为 true 的节点', visible: function() {return this.IsVisible(1)}})
    isOnlyActiveTrue: boolean = false;
    @property({displayName: '是否翻转', tooltip: '将目标节点的 scaleX 取反', visible: function() {return this.IsVisible(1)}})
    isFlipX: boolean = false;
    @property({displayName: '是否同步坐标', tooltip: '同步修改节点的 position 属性，使其和本节点相同', visible: function() {return this.IsVisible(1)}})
    isPositionSync: boolean = false;
    @property({displayName: '是否同步尺寸', tooltip: '同步修改节点的 size 属性，使其和本节点相同', visible: function() {return this.IsVisible(1)}})
    isSizeSync: boolean = true;
    @property({displayName: '是否同步颜色', tooltip: '同步修改节点的 color 属性，使其和本节点相同', visible: function() {return this.IsVisible(1)}})
    isColorSync: boolean = true;


    @property({displayName: '> 重命名子节点', tooltip: '按顺序重命名子节点，替换带“下划线 _” + “数字”的部分，没有则会添加', visible: function() {return this.IsVisible(2)}})
    get renameChildNodes(): boolean {
        return false;
    }
    set renameChildNodes(value: boolean) {
        this.RenameChildNodes();
    }
    @property({displayName: '重命名前缀', tooltip: '重命名的前缀，此项留空则不进行重命名，只添加编号', visible: function() {return this.IsVisible(2)}})
    renamePrefix: string = '';
    // 是否添加编号
    @property({displayName: '是否添加编号', tooltip: '是否在重命名时添加编号', visible: function() {return this.IsVisible(2)}})
    isAddNumber: boolean = true;
    @property({displayName: '起始编号', tooltip: '编号初始值，第一个编号为此数字，之后累加', visible: function() {return this.IsVisible(2)}})
    numberStart: number = 1;
    // 是否只为重名的节点添加编号
    @property({displayName: '只为重名的节点添加编号', tooltip: '只为重名的节点添加编号', visible: function() {return this.IsVisible(2)}})
    isOnlyForSameName: boolean = false;
    // 是否开启独立顺序，独立顺序是指子节点的第一个会重新开始编号
    @property({displayName: '是否开启独立顺序', tooltip: '开启独立顺序后，子节点的第一个子节点会重新开始编号（递归）', visible: function() {return this.IsVisible(2)}})
    isNumberIndependent: boolean = true;
    // 遍历深度
    @property({displayName: '遍历深度', tooltip: '重命名的遍历深度。\n0 为仅本节点之后的所有兄弟节点（未开启）\n1 为所有子节点\n2 为所有子节点及其子节点...\
\n-1 为递归所有的子节点', visible: function() {return this.IsVisible(2)}, type: cc.Integer})
    searchDepth: number = 1;

    @property({displayName: '> 搜索使用本节点的节点', tooltip: '搜索使用本节点的节点', visible: function() {return this.IsVisible(3)}})
    get searchUsingSelf(): boolean {
        return false;
    }
    set searchUsingSelf(value: boolean) {
        this.SearchUsingSelf();
    }

    @property({displayName: '搜索起点节点', tooltip: '搜索的起点为此节点，输出其下所有引用本节点的属性\n如未指定，则搜索全场景', visible: function() {return this.IsVisible(3)}, type: cc.Node})
    searchTargetNode: cc.Node = null;

    @property({displayName: '搜索起点节点子节点的深度', tooltip: '处于深度以外的子节点会被忽略\n-1 为不限制深度\n0 为不搜索子节点', visible: function() {return this.IsVisible(3)}, type: cc.Integer, range: [-1, Infinity, 1]})
    searchTargetNodeDepth: number = -1;

    @property({displayName: '是否包含子节点', tooltip: '搜索时，也会同时搜索本节点的子节点及所有组件', visible: function() {return this.IsVisible(3)}})
    isIncludeSelfChildren: boolean = false;

    @property({displayName: '包含子节点的深度', tooltip: '处于深度以外的子节点会被忽略\n-1 为不限制深度', visible: function() {return this.IsVisible(3)}, type: cc.Integer, range: [-1, Infinity, 1]})
    includeSelfChildrenDepth: number = 1;



    IsVisible(ref: number) {
        if(ref == 0) {
            return this.commandsGroup == CommandsGroupType.example;
        } else if(ref == 1) {
            return this.commandsGroup == CommandsGroupType.spriteSync;
        } else if(ref == 2) {
            return this.commandsGroup == CommandsGroupType.renameNodes;
        } else if(ref == 3) {
            return this.commandsGroup == CommandsGroupType.searchUsingSelf;
        } else {
            return false;
        }
    }

    /** 在加载时执行 */
    onLoad() {
        if(CC_EDITOR) {
            // cc.log('自定义命令脚本已被添加 !!');
            this.LoadNodeSrcTexture();
        }
    }

    CallExampleCommand() {
        cc.log('Call Example Command !!');
    }

    LoadNodeSrcTexture() {
        let spriteScript = this.node.getComponent(cc.Sprite);
        if(spriteScript && spriteScript.spriteFrame) {
            let spriteTex = spriteScript.spriteFrame.getTexture();
            if(spriteTex) { this.sourceTexture = spriteTex; }
        }
    }

    ReplaceAllSceneTexture() {
        cc.log('执行：替换精灵纹理...');
        if(!this.sourceTexture) {
            cc.warn('执行中止 ! 源纹理为空 !!');
            return;
        }
        let rootNode: cc.Node = cc.director.getScene();
        if(this.isOnlyThisNodeChildren) {
            rootNode = this.node;
        }
        let spriteScripts: cc.Sprite[] = rootNode.getComponentsInChildren(cc.Sprite);
        let count = 0;
        try {
            spriteScripts.forEach((e)=>{
                // cc.log(`---sprite (${count})`);
                if(this.isOnlyActiveTrue && !e.node.activeInHierarchy) {
                    return;
                }
                if(e.spriteFrame && e.spriteFrame.getTexture() == this.sourceTexture) {
                    if(this.targetSpriteFrame) {
                        this.ReplaceTextrue(e, this.targetSpriteFrame);
                    }
                    if(this.isFlipX) {
                        e.node.scaleX = -e.node.scaleX;
                    }
                    if(this.isPositionSync) {
                        e.node.setPosition(this.node.x, this.node.y);
                    }
                    if(this.isSizeSync) {
                        e.node.setContentSize(this.node.width, this.node.height);
                    }
                    if(this.isColorSync) {
                        e.node.color = this.node.color;
                    }
                    count ++;
                }
            });
        } catch (error) {
            cc.error(error);
        }
        cc.log(`执行完成：替换精灵纹理 ！ 同步节点数：${count}`);
    }

    ReplaceTextrue(sprite: cc.Sprite, targetSpriteFrame: cc.SpriteFrame) {
        sprite.spriteFrame = targetSpriteFrame;
        // // @ts-ignore
        // await Editor.Ipc.sendToPanel('scene', 'scene:set-property',{
        //     id: sprite.uuid,
        //     path: "spriteFrame",//要修改的属性
        //     type: "cc.SpriteFrame",
        //     value: { uuid: this.LoadAssets(targetSpriteFrame.url) },
        //     isSubProp: false,
        // }, ()=>{
        //     cc.log('替换精灵纹理 ！');
        // }, 2);
    }
    
    LoadAssets(url: string): string {
        // cc.Editor.remote.assetdb.urlToUuid('db://assets/Scene/Main.fire');
        // @ts-ignore
        return Editor.remote.assetdb.urlToUuid('db://assets/' + url);
    }


    RenameChildNodes() {
        cc.log('执行：重命名子节点...');
        let rootNode = this.node;
        // if(this.searchDepth == 0) {
            // let nodes: cc.Node[] = [];
            // let isRenameLeft = false;
            // for(let i = 0; i < rootNode.parent.children.length; i++) {
            //     if(!isRenameLeft && rootNode.parent.children[i].uuid == rootNode.uuid) {
            //         isRenameLeft = true;
            //     }
            //     if(isRenameLeft) {
            //         nodes.push(rootNode.parent.children[i]);
            //     }
            // }
            // this.RenameNodes(nodes);
        // } else {
            let count = this.RenameNodes(rootNode.children, this.renamePrefix, this.isAddNumber, this.numberStart, this.isOnlyForSameName, this.isNumberIndependent,
                  this.searchDepth == -1 || this.searchDepth > 1, this.searchDepth);
        // }
        cc.log(`执行完成：重命名子节点 ！ 重命名节点数：${count}`);
    }

    RenameNodes(nodes: cc.Node[], renamePrefix: string = '', isAddNumber: boolean = true, numberStart: number = 1, isOnlyForSameName: boolean = false,
          isNumberIndependent: boolean = false, isRenameChildren: boolean = false, searchDepth: number = 0): number {
        if(renamePrefix == '' && !isAddNumber) {
            cc.warn('执行中止 ! 未设置重命名前缀且未开启添加编号 !!');
            return;
        }
        let sameTimes: StringDictionary<number> = {};

        let nowNumber = numberStart;

        let renameNodesCount = 0;
        let nameStringList: string[] = [];
        let numberList: number[] = [];

        nodes.forEach((e, i)=>{
            let nameString = renamePrefix == '' ? e.name : renamePrefix;
            // 首先查看原先字符串中是否带下划线加数字组合
            if(nameString != '') {
                let charIndex = this.SearchLastUnderlineChar(nameString);
                if(charIndex != -1) {
                    let numString = nameString.slice(charIndex + 1, nameString.length);
                    let num = parseInt(numString);
                    if(!Number.isNaN(num)) {
                        nameString = nameString.slice(0, charIndex);
                    }
                }
            }
            sameTimes[nameString] = (sameTimes[nameString] || 0) + 1;
            
            nameStringList.push(nameString);
            numberList.push(nowNumber);
            
            nowNumber += 1;
            renameNodesCount += 1;

            if(e.children.length > 0) {
                if(isRenameChildren) {
                    let count = this.RenameNodes(e.children, renamePrefix, isAddNumber, isNumberIndependent? numberStart : nowNumber, isOnlyForSameName, isNumberIndependent,
                          searchDepth == -1 || searchDepth - 1 > 0, (searchDepth <= 0) ? searchDepth : searchDepth - 1);
                    if(!isNumberIndependent) {
                        nowNumber += count;
                    }
                    renameNodesCount += count;
                }
            }
        });

        let stringAddList: string[] = [];
        if(isOnlyForSameName) {
            nameStringList.forEach((e, i)=>{
                if(sameTimes[e] > 1) {
                    stringAddList[i] = '_' + numberList[i];
                } else {
                    stringAddList[i] = '';
                }
            });
        } else {
            nameStringList.forEach((e, i)=>{
                stringAddList[i] = '_' + numberList[i];
            });
        }
        nodes.forEach((e, i)=>{
            if(isAddNumber) {
                e.name = nameStringList[i] + stringAddList[i];
            } else {
                e.name = renamePrefix;
            }
        });

        return renameNodesCount;
    }

    /** 查找最后一个下划线 “_” 字符的位置 */
    SearchLastUnderlineChar(string: string): number {
        let chars: string[] = string.split('');
        let charIndex = -1;
        for(let j = chars.length - 1; j >= 0; j--) {
            if(charIndex < 0 && chars[j] == '_') {
                charIndex = j;
            }
        }
        return charIndex;
    }

    SearchUsingSelf() {
        cc.log('执行：搜索使用本节点的节点...');
        this.SearchUsingSelfSync();
    }

    async SearchUsingSelfSync() {
        let target = this.searchTargetNode;
        
        await this.SceneNodeDump(this.node, (rtn)=>{
            console.log('value: ', rtn.value);
            let comps = rtn.value.__comps__;
            comps.forEach((e, i)=>{
                cc.log(this.LogCleanString(`[${i}] componet 基本信息: \
                    \n[url]: ${e.type}\
                    \n[type]: ${e.type}\
                    \n[name]: ${e.value.name.value}\
                    \n[uuid]: ${e.value.uuid.value}\
                    \n[nodeName]: ${e.value.node.value.name}\
                    \n[nodeUuid]: ${e.value.node.value.uuid}\
                \n`));
                // console.log(`[${i}] componet 基本信息: \n\
                //     [name]: %o\n\
                //     [uuid]: %o\n\
                //     [nodeName]: %o\n\
                //     [nodeUuid]: %o\n\n\
                // `, e.value.name, e.value.uuid, e.value.node.value.name, e.value.node.value.uuid);
            });
        });
        // await this.LoadAllComponets();
    }

    async LoadAllComponets(getter?: (result: any)=>void) {
        let sceneRoot = cc.director.getScene();
        await this.SceneNodeDump(this.node, (rtn)=>{
            cc.log('rtn0: ', rtn);
            console.log('rtn0: ', rtn);
            console.log('rtn0 => value: ', rtn.value);
        });
        // await this.SceneNodeDump(sceneRoot.children[0], (rtn)=>{
        //     cc.log('rtn1:', rtn);
        //     console.log('rtn1:', rtn);
        // });
        // await this.LoadNodeComponets(sceneRoot.children[0], (rtn)=>{
        //     cc.log('rtn2:', rtn);
        //     console.log('rtn2:', rtn);
        // });
    }

    async LoadNodeComponets(node: cc.Node, getter?: (result: any)=>void) {
        let comps = node.getComponents(cc.Component);
        let results = [];
        for(let i = 0; i < comps.length; i++) {
            let comp = comps[i];
            await this.SceneComponentDump(comp, (rtn)=>{
                results.push(rtn);
            });
        }
        if(getter) {
            getter(results);
        }
    }

    /** 获取一个节点的场景数据 */
    async SceneNodeDump(node: cc.Node, getter?: (result: Dump)=>void) {
        const Editor = window['Editor'];
        await Editor.Ipc.sendToPanel('scene', 'scene:query-node', node.uuid, (...rtn: any[])=>{
            if(getter) {
                if(!rtn) {
                    getter(null);
                } else {
                    getter(JSON.parse(rtn[1]));
                }
            }
        }, 2000);
    }

    /** 获取一个组件的场景数据 */
    async SceneComponentDump(component: cc.Component, getter?: (result: Dump)=>void) {
        const Editor = window['Editor'];
        await Editor.Ipc.sendToPanel('scene', 'scene:query-component', component.uuid, (...rtn: any[])=>{
            if(getter) {
                if(!rtn) {
                    getter(null);
                } else {
                    getter(JSON.parse(rtn[1]));
                }
            }
        }, 2000);
    }

    
    LogCleanString(str: string) {
        // 使用正则去除每行的空白
        // const cleanStartSpaceStr = str.replace(/^[ \t]+/gm, '');
        // const cleanEndSpaceStr = str.replace(/[ \t]+$/gm, '');
        const cleanStr = str.replace(/^\s+|\s+$/gm, '');
        return cleanStr;
    }
}

export type ObjectProps<T> = {
    [name: string]: T
}

export type DumpCell = {
    type: string;
    value: ObjectProps<DumpCell> | ObjectProps<any>;
    visible: boolean;
}

export type Dump = {
    types: cc.Node[];
    value: {
        __comps__: Array<DumpCell>;
        __type__: string;
    }
}